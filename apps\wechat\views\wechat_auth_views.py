#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : wechat_auth_views.py
<AUTHOR> JT_DA
@Date     : 2025/07/23
@File_Desc: 微信认证相关视图，实现与外部认证服务器的微信登录和令牌刷新功能
"""

import logging
import requests
from rest_framework.generics import GenericAPIView
from rest_framework import serializers
from rest_framework.permissions import AllowAny

from ops_management.settings import AUTH_SERVER_URL
from utils.ajax_result import AjaxResult
from apps.wechat.serializers import WechatLoginSerializer, WechatRefreshSerializer
from utils.permission_helper import WechatBasicPermission

# 获取日志记录器
logger = logging.getLogger(__name__)


class WechatLoginView(GenericAPIView):
    """
    微信登录视图

    对接外部认证服务器的微信登录接口，处理微信小程序的用户登录认证。
    通过微信授权码、用户昵称和头像信息完成用户身份验证和登录流程。
    """

    authentication_classes = []
    permission_classes = [AllowAny]

    serializer_class = WechatLoginSerializer

    def post(self, request):
        """
        处理微信登录请求

        接收微信小程序的授权信息，调用外部认证服务器完成用户登录认证。
        验证请求参数后，将登录信息转发给认证服务器，并透传响应结果。

        **请求参数：**
        **请求体参数：**
        - js_code (字符串, 必需): 微信小程序授权码，用于获取用户openid和session_key，最大长度128位
        - nickname (字符串, 必需): 用户昵称，微信用户的显示名称，最大长度64位
        - avatar_url (字符串, 必需): 用户头像URL，必须为有效的URL格式，最大长度512位

        **请求数据示例：**
        ```json
        {
            "js_code": "0c1a2b3c4d5e6f7g8h9i",
            "nickname": "微信用户",
            "avatar_url": "https://wx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTKxrUx8UqRd3h5A9mSltZEurxodGlFfcvqrAJoR32dHOu61UDeGFYIrpvA6fwqFYuufcUR557odeA/132"
        }
        ```

        **响应数据结构：**
        ```json
        {
            "code": 200,
            "msg": "登录成功",
            "state": "success",
            "data": {
                "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
                "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
                "expires_in": 3600,
                "user_info": {
                    "openid": "oGZUI0egBJY1zhBYw2KhdUfwVJJE",
                    "nickname": "微信用户",
                    "avatar_url": "https://wx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTKxrUx8UqRd3h5A9mSltZEurxodGlFfcvqrAJoR32dHOu61UDeGFYIrpvA6fwqFYuufcUR557odeA/132"
                }
            }
        }
        ```

        **错误响应示例：**
        ```json
        {
            "code": 400,
            "msg": "微信授权码不能为空",
            "state": "fail",
            "data": null
        }
        ```
        """
        # 使用序列化器验证请求数据
        serializer = self.get_serializer(data=request.data)
        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as e:
            # 记录验证失败日志
            logger.warning(f"微信登录请求参数验证失败: {e}")
            # 提取第一个错误信息返回给用户
            error_msg = "参数验证失败"
            if hasattr(e, "detail"):
                if isinstance(e.detail, dict):
                    # 获取第一个字段的第一个错误信息
                    for field_errors in e.detail.values():
                        if isinstance(field_errors, list) and field_errors:
                            error_msg = str(field_errors[0])
                            break
                elif isinstance(e.detail, list) and e.detail:
                    error_msg = str(e.detail[0])
            return AjaxResult.fail(msg=error_msg)

        # 获取验证通过的数据
        validated_data = serializer.validated_data
        login_data = {
            "js_code": validated_data["js_code"],
            "nickname": validated_data["nickname"],
            "avatar_url": validated_data["avatar_url"],
        }

        try:
            # 调用外部认证服务器微信登录接口
            login_url = f"{AUTH_SERVER_URL}/wechat/login/"
            response = requests.post(
                login_url, json=login_data, headers={"Content-Type": "application/json"}, timeout=5  # 设置5秒超时
            )

            # 记录请求日志
            logger.info(f"微信登录请求，认证服务器响应状态码: {response.status_code}")

            # 直接返回外部认证服务器的响应数据和状态码
            try:
                response_data = response.json()
                logger.info(f"微信登录响应: {response_data}")
                # 直接透传外部服务器的响应
                return AjaxResult(
                    code=response_data.get("code", response.status_code),
                    msg=response_data.get("msg", ""),
                    state=response_data.get("state", ""),
                    data=response_data.get("data"),
                ).to_json_response()
            except ValueError:
                # 响应不是有效的JSON格式
                logger.warning("外部认证服务器返回非JSON格式响应")
                return AjaxResult(code=response.status_code, msg="服务器响应格式错误", state="fail").to_json_response()

        except requests.exceptions.Timeout:
            # 请求超时处理
            logger.error("调用认证服务器微信登录接口超时")
            return AjaxResult.fail(msg="请求超时，请稍后重试")
        except requests.exceptions.RequestException as e:
            # 网络请求异常处理
            logger.error(f"调用认证服务器微信登录接口失败: {str(e)}")
            return AjaxResult.fail(msg="网络连接异常，请稍后重试")
        except Exception as e:
            # 其他异常处理
            logger.error(f"微信登录过程中发生异常: {str(e)}")
            return AjaxResult.server_error(msg="服务器内部错误，请联系管理员")


class WechatRefreshView(GenericAPIView):
    """
    微信刷新令牌视图

    对接外部认证服务器的微信session_key刷新接口，用于刷新微信用户的会话密钥。
    确保微信小程序用户的会话状态保持有效，无需重新登录即可继续使用服务。
    """

    permission_classes = [WechatBasicPermission]
    
    serializer_class = WechatRefreshSerializer

    def post(self, request):
        """
        处理微信session_key刷新请求

        调用外部认证服务器刷新微信用户的session_key，延长用户会话有效期。
        此接口无需额外参数，通过Authorization头中的token识别用户身份并刷新会话。

        **请求参数：**
        **请求头参数：**
        - Authorization (字符串, 必需): 用户认证令牌，格式为 "Bearer token_string"

        **请求数据示例：**
        ```json
        {}
        ```

        **响应数据结构：**
        ```json
        {
            "code": 200,
            "msg": "刷新成功",
            "state": "success",
            "data": {
                "session_key": "wechat_session_key_string",
                "expires_in": 7200,
                "refresh_time": "2025-07-23 14:30:00"
            }
        }
        ```

        **错误响应示例：**
        ```json
        {
            "code": 401,
            "msg": "未授权",
            "state": "fail",
            "data": null
        }
        ```
        """
        # 使用序列化器验证请求数据（虽然无需额外参数，但保持API一致性）
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        # 从请求头获取认证token
        auth_header = request.headers.get("Authorization")
        if not auth_header:
            logger.warning("微信刷新令牌请求缺少Authorization头")
            return AjaxResult.unauthorized()

        try:
            # 调用外部认证服务器微信刷新接口
            refresh_url = f"{AUTH_SERVER_URL}/wechat/refresh/"
            response = requests.post(
                refresh_url,
                headers={"Authorization": auth_header, "Content-Type": "application/json"},
                json={},  # 无需额外参数
                timeout=5,  # 设置5秒超时
            )

            # 记录请求日志
            logger.info(f"微信刷新令牌请求，认证服务器响应状态码: {response.status_code}")

            # 直接返回外部认证服务器的响应数据和状态码
            try:
                response_data = response.json()
                logger.info(f"微信刷新令牌响应: {response_data}")
                # 直接透传外部服务器的响应
                return AjaxResult(
                    code=response_data.get("code", response.status_code),
                    msg=response_data.get("msg", ""),
                    state=response_data.get("state", ""),
                    data=response_data.get("data"),
                ).to_json_response()
            except ValueError:
                # 响应不是有效的JSON格式
                logger.warning("外部认证服务器返回非JSON格式响应")
                return AjaxResult(code=response.status_code, msg="服务器响应格式错误", state="fail").to_json_response()

        except requests.exceptions.Timeout:
            # 请求超时处理
            logger.error("调用认证服务器微信刷新接口超时")
            return AjaxResult.fail(msg="请求超时，请稍后重试")
        except requests.exceptions.RequestException as e:
            # 网络请求异常处理
            logger.error(f"调用认证服务器微信刷新接口失败: {str(e)}")
            return AjaxResult.fail(msg="网络连接异常，请稍后重试")
        except Exception as e:
            # 其他异常处理
            logger.error(f"微信session_key刷新过程中发生异常: {str(e)}")
            return AjaxResult.server_error(msg="服务器内部错误，请联系管理员")
